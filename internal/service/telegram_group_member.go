package service

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"

	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/model/entity"
)

// telegramGroupMemberService manages telegram group members.
type telegramGroupMemberService struct{}

var (
	insGroupMember = &telegramGroupMemberService{}
)

// TelegramGroupMember returns service instance.
func TelegramGroupMember() *telegramGroupMemberService {
	return insGroupMember
}

// AddMember inserts a new member record if not exists.
func (s *telegramGroupMemberService) AddMember(ctx context.Context, chatID int64, tenantID uint, member entity.TelegramGroupMembers) error {
	// ensure mandatory fields
	member.ChatId = chatID
	member.TenantId = tenantID
	if member.JoinedAt == nil {
		member.JoinedAt = gtime.Now()
	}

	// Upsert like behaviour
	_, err := dao.TelegramGroupMembers.Ctx(ctx).Data(member).
		OnDuplicate("username, first_name, last_name, is_bot, joined_at, updated_at", member.Username, member.FirstName, member.LastName, member.IsBot, member.JoinedAt, gtime.Now()).
		Insert()
	if err != nil {
		return gerror.WrapCode(gcode.CodeDbOperationError, err, "failed to add group member")
	}
	return nil
}

// RemoveMember deletes record by PK.
func (s *telegramGroupMemberService) RemoveMember(ctx context.Context, chatID int64, tenantID uint, userID int64) error {
	_, err := dao.TelegramGroupMembers.Ctx(ctx).
		Where(dao.TelegramGroupMembers.Columns().ChatId, chatID).
		//Where(dao.TelegramGroupMembers.Columns().TenantId, tenantID).
		Where(dao.TelegramGroupMembers.Columns().UserId, userID).
		Delete()
	if err != nil {
		return gerror.WrapCode(gcode.CodeDbOperationError, err, "failed to remove group member")
	}
	return nil
}

// GetGroupMembers retrieves all members of a specific group.
func (s *telegramGroupMemberService) GetGroupMembers(ctx context.Context, chatID int64, tenantID uint) ([]*entity.TelegramGroupMembers, error) {
	var members []*entity.TelegramGroupMembers
	err := dao.TelegramGroupMembers.Ctx(ctx).
		Where(dao.TelegramGroupMembers.Columns().ChatId, chatID).
		//Where(dao.TelegramGroupMembers.Columns().TenantId, tenantID).
		OrderAsc(dao.TelegramGroupMembers.Columns().JoinedAt).
		Scan(&members)
	if err != nil {
		return nil, gerror.WrapCode(gcode.CodeDbOperationError, err, "failed to get group members")
	}
	return members, nil
}

// GetMemberCount returns the count of members in a specific group.
func (s *telegramGroupMemberService) GetMemberCount(ctx context.Context, chatID int64, tenantID uint) (int, error) {
	count, err := dao.TelegramGroupMembers.Ctx(ctx).
		Where(dao.TelegramGroupMembers.Columns().ChatId, chatID).
		//Where(dao.TelegramGroupMembers.Columns().TenantId, tenantID).
		Count()
	if err != nil {
		return 0, gerror.WrapCode(gcode.CodeDbOperationError, err, "failed to count group members")
	}
	return count, nil
}

// IsMemberInGroup checks if a user is a member of a specific group.
func (s *telegramGroupMemberService) IsMemberInGroup(ctx context.Context, chatID int64, tenantID uint, userID int64) (bool, error) {
	count, err := dao.TelegramGroupMembers.Ctx(ctx).
		Where(dao.TelegramGroupMembers.Columns().ChatId, chatID).
		// //Where(dao.TelegramGroupMembers.Columns().TenantId, tenantID).
		Where(dao.TelegramGroupMembers.Columns().UserId, userID).
		Count()
	if err != nil {
		return false, gerror.WrapCode(gcode.CodeDbOperationError, err, "failed to check group membership")
	}
	return count > 0, nil
}
